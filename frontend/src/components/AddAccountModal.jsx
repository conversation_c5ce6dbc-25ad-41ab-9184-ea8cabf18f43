import React, { useState } from 'react'

function AddAccountModal({ isOpen, onClose, onSubmit, wsClient }) {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    useGoogleLogin: false,
    proxy: {
      host: '',
      port: '',
      username: '',
      password: '',
      country: '',
      city: ''
    }
  })

  const [proxyStatus, setProxyStatus] = useState(null) // null, 'testing', 'active', 'inactive'
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (field, value) => {
    if (field.startsWith('proxy.')) {
      const proxyField = field.split('.')[1]
      setFormData(prev => ({
        ...prev,
        proxy: {
          ...prev.proxy,
          [proxyField]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
  }

  const handleTestProxy = async () => {
    const { host, port, username, password } = formData.proxy
    
    if (!host || !port) {
      alert('<PERSON>ui lòng nhập đầy đủ thông tin proxy (Host và Port)')
      return
    }

    setProxyStatus('testing')
    
    try {
      // Gửi request test proxy đến backend
      wsClient.sendCommand('test_proxy', {
        proxy: {
          host: host.trim(),
          port: parseInt(port),
          username: username.trim() || null,
          password: password.trim() || null
        }
      })

      // Lắng nghe response
      const handleProxyTestResult = (message) => {
        if (message.type === 'proxy_test_result') {
          setProxyStatus(message.isActive ? 'active' : 'inactive')
          wsClient.off('proxy_test_result', handleProxyTestResult)
        }
      }

      wsClient.on('proxy_test_result', handleProxyTestResult)

      // Timeout sau 10 giây
      setTimeout(() => {
        if (proxyStatus === 'testing') {
          setProxyStatus('inactive')
          wsClient.off('proxy_test_result', handleProxyTestResult)
        }
      }, 10000)

    } catch (error) {
      console.error('Error testing proxy:', error)
      setProxyStatus('inactive')
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    // Validation
    if (!formData.useGoogleLogin && (!formData.username || !formData.password)) {
      alert('Vui lòng nhập username và password hoặc chọn đăng nhập bằng Google')
      return
    }

    if (!formData.proxy.host || !formData.proxy.port) {
      alert('Vui lòng nhập thông tin proxy')
      return
    }

    setIsSubmitting(true)

    try {
      const accountData = {
        username: formData.useGoogleLogin ? null : formData.username.trim(),
        password: formData.useGoogleLogin ? null : formData.password.trim(),
        useGoogleLogin: formData.useGoogleLogin,
        proxy: {
          host: formData.proxy.host.trim(),
          port: parseInt(formData.proxy.port),
          username: formData.proxy.username.trim() || null,
          password: formData.proxy.password.trim() || null,
          country: formData.proxy.country.trim() || null,
          city: formData.proxy.city.trim() || null
        }
      }

      await onSubmit(accountData)
      handleClose()
    } catch (error) {
      console.error('Error creating account:', error)
      alert('Có lỗi xảy ra khi tạo tài khoản')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    setFormData({
      username: '',
      password: '',
      useGoogleLogin: false,
      proxy: {
        host: '',
        port: '',
        username: '',
        password: '',
        country: '',
        city: ''
      }
    })
    setProxyStatus(null)
    setIsSubmitting(false)
    onClose()
  }

  const getProxyStatusText = () => {
    switch (proxyStatus) {
      case 'testing':
        return 'Đang kiểm tra...'
      case 'active':
        return 'Hoạt động'
      case 'inactive':
        return 'Không hoạt động'
      default:
        return ''
    }
  }

  if (!isOpen) return null

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3 className="modal-title">Thêm tài khoản mới</h3>
          <button className="modal-close" onClick={handleClose}>
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            {/* Google Login Option */}
            <div className="checkbox-group">
              <input
                type="checkbox"
                id="useGoogleLogin"
                checked={formData.useGoogleLogin}
                onChange={(e) => handleInputChange('useGoogleLogin', e.target.checked)}
              />
              <label htmlFor="useGoogleLogin">
                Đăng nhập bằng Google (bỏ qua username/password)
              </label>
            </div>

            {/* Account Information */}
            {!formData.useGoogleLogin && (
              <>
                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">Username *</label>
                    <input
                      type="text"
                      className="form-input"
                      value={formData.username}
                      onChange={(e) => handleInputChange('username', e.target.value)}
                      placeholder="Nhập username"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">Password *</label>
                    <input
                      type="password"
                      className="form-input"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      placeholder="Nhập password"
                      required
                    />
                  </div>
                </div>
              </>
            )}

            {/* Proxy Information */}
            <h4 style={{ margin: '24px 0 16px 0', color: '#374151', fontSize: '16px' }}>
              Thông tin Proxy *
            </h4>
            
            <div className="form-row">
              <div className="form-group">
                <label className="form-label">Host *</label>
                <input
                  type="text"
                  className="form-input"
                  value={formData.proxy.host}
                  onChange={(e) => handleInputChange('proxy.host', e.target.value)}
                  placeholder="*************"
                  required
                />
              </div>
              <div className="form-group">
                <label className="form-label">Port *</label>
                <input
                  type="number"
                  className="form-input"
                  value={formData.proxy.port}
                  onChange={(e) => handleInputChange('proxy.port', e.target.value)}
                  placeholder="8080"
                  required
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label className="form-label">Username (tùy chọn)</label>
                <input
                  type="text"
                  className="form-input"
                  value={formData.proxy.username}
                  onChange={(e) => handleInputChange('proxy.username', e.target.value)}
                  placeholder="Proxy username"
                />
              </div>
              <div className="form-group">
                <label className="form-label">Password (tùy chọn)</label>
                <input
                  type="password"
                  className="form-input"
                  value={formData.proxy.password}
                  onChange={(e) => handleInputChange('proxy.password', e.target.value)}
                  placeholder="Proxy password"
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label className="form-label">Quốc gia (tùy chọn)</label>
                <input
                  type="text"
                  className="form-input"
                  value={formData.proxy.country}
                  onChange={(e) => handleInputChange('proxy.country', e.target.value)}
                  placeholder="US, GB, VN..."
                />
              </div>
              <div className="form-group">
                <label className="form-label">Thành phố (tùy chọn)</label>
                <input
                  type="text"
                  className="form-input"
                  value={formData.proxy.city}
                  onChange={(e) => handleInputChange('proxy.city', e.target.value)}
                  placeholder="New York, London..."
                />
              </div>
            </div>

            {/* Proxy Test */}
            <div className="proxy-test-container">
              <button
                type="button"
                className="btn btn-secondary btn-sm"
                onClick={handleTestProxy}
                disabled={proxyStatus === 'testing' || !formData.proxy.host || !formData.proxy.port}
              >
                {proxyStatus === 'testing' ? 'Đang kiểm tra...' : 'Test Proxy'}
              </button>
              
              {proxyStatus && (
                <span className={`proxy-status ${proxyStatus}`}>
                  {getProxyStatusText()}
                </span>
              )}
            </div>
          </div>

          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Hủy
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Đang tạo...' : 'Tạo tài khoản'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AddAccountModal
