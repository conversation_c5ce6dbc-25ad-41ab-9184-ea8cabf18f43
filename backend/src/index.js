const path = require('path');
const DatabaseManager = require('./database/manager');
const WebSocketServer = require('./server/websocket');
const TikTokLoginAutomation = require('./automation/login');
const FollowInteractAutomation = require('./automation/follow-interact');
const {
  readAccountsFile,
  readProxiesFile,
  readCommentsFile,
  formatTime
} = require('./utils');

class TikTokAutomationBackend {
  constructor() {
    this.db = new DatabaseManager();
    this.wsServer = new WebSocketServer(8080);
    this.loginAutomation = null;
    this.followInteractAutomation = null;
    this.isRunning = false;
  }

  /**
   * Khởi tạo backend
   */
  async initialize() {
    console.log(`[${formatTime()}] TikTok Automation Backend starting...`);

    try {
      // Khởi tạo database
      await this.db.initialize();
      console.log(`[${formatTime()}] Database initialized`);

      // Khởi tạo WebSocket server
      this.wsServer.start();

      // Khởi tạo automation modules
      this.loginAutomation = new TikTokLoginAutomation(this.wsServer, this.db);
      this.followInteractAutomation = new FollowInteractAutomation(this.wsServer, this.db);

      // Đăng ký các command handlers
      this.registerCommandHandlers();

      this.isRunning = true;
      console.log(`[${formatTime()}] Backend started successfully`);

    } catch (error) {
      console.error(`[${formatTime()}] Failed to start backend:`, error);
      process.exit(1);
    }
  }

  /**
   * Đăng ký các command handlers cho WebSocket
   */
  registerCommandHandlers() {
    // Command: Load accounts từ file
    this.wsServer.onCommand('load_accounts', async (ws, data) => {
      try {
        const filePath = data.filePath || path.join(__dirname, '../data/accounts.txt');
        const accounts = await readAccountsFile(filePath);

        if (accounts.length > 0) {
          await this.db.addAccounts(accounts);
          this.wsServer.sendSuccess(ws, `Loaded ${accounts.length} accounts successfully`);
          this.wsServer.sendLog('info', `Loaded ${accounts.length} accounts from file`);
        } else {
          this.wsServer.sendError(ws, 'No valid accounts found in file');
        }
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to load accounts: ${error.message}`);
      }
    });

    // Command: Load proxies từ file
    this.wsServer.onCommand('load_proxies', async (ws, data) => {
      try {
        const filePath = data.filePath || path.join(__dirname, '../data/proxies.txt');
        const proxies = await readProxiesFile(filePath);

        if (proxies.length > 0) {
          await this.db.addProxies(proxies);
          this.wsServer.sendSuccess(ws, `Loaded ${proxies.length} proxies successfully`);
          this.wsServer.sendLog('info', `Loaded ${proxies.length} proxies from file`);
        } else {
          this.wsServer.sendError(ws, 'No valid proxies found in file');
        }
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to load proxies: ${error.message}`);
      }
    });

    // Command: Load comments từ file
    this.wsServer.onCommand('load_comments', async (ws, data) => {
      try {
        const filePath = data.filePath || path.join(__dirname, '../data/comments.txt');
        const comments = await readCommentsFile(filePath);

        if (comments.length > 0) {
          await this.db.addComments(comments);
          this.wsServer.sendSuccess(ws, `Loaded ${comments.length} comments successfully`);
          this.wsServer.sendLog('info', `Loaded ${comments.length} comments from file`);
        } else {
          this.wsServer.sendError(ws, 'No valid comments found in file');
        }
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to load comments: ${error.message}`);
      }
    });

    // Command: Get accounts
    this.wsServer.onCommand('get_accounts', async (ws, data) => {
      try {
        const accounts = await this.db.getAccounts();
        this.wsServer.sendSuccess(ws, 'Accounts retrieved successfully', { accounts });
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to get accounts: ${error.message}`);
      }
    });

    // Command: Get settings
    this.wsServer.onCommand('get_settings', async (ws, data) => {
      try {
        const settings = await this.db.getSettings();
        this.wsServer.sendSuccess(ws, 'Settings retrieved successfully', { settings });
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to get settings: ${error.message}`);
      }
    });

    // Command: Update settings
    this.wsServer.onCommand('update_settings', async (ws, data) => {
      try {
        const settings = await this.db.updateSettings(data.settings);
        this.wsServer.sendSuccess(ws, 'Settings updated successfully', { settings });
        this.wsServer.sendLog('info', 'Settings updated');
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to update settings: ${error.message}`);
      }
    });

    // Command: Start automation
    this.wsServer.onCommand('start_automation', async (ws, data) => {
      try {
        const { accountIds, targetProfile } = data;

        if (!accountIds || !Array.isArray(accountIds) || accountIds.length === 0) {
          this.wsServer.sendError(ws, 'Account IDs array is required');
          return;
        }

        if (!targetProfile) {
          this.wsServer.sendError(ws, 'Target profile URL is required');
          return;
        }

        this.wsServer.sendLog('info', `Starting automation for ${accountIds.length} accounts targeting ${targetProfile}`);

        // Start automation in background
        this.followInteractAutomation.startAutomation(accountIds, targetProfile)
          .catch(error => {
            this.wsServer.sendLog('error', `Automation error: ${error.message}`);
          });

        this.wsServer.sendSuccess(ws, 'Automation started successfully');
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to start automation: ${error.message}`);
      }
    });

    // Command: Stop automation
    this.wsServer.onCommand('stop_automation', async (ws, data) => {
      try {
        this.wsServer.sendLog('info', 'Stopping automation...');
        await this.followInteractAutomation.stopAutomation();
        this.wsServer.sendSuccess(ws, 'Automation stopped successfully');
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to stop automation: ${error.message}`);
      }
    });

    // Command: Login account
    this.wsServer.onCommand('login_account', async (ws, data) => {
      try {
        const { accountId } = data;
        if (!accountId) {
          this.wsServer.sendError(ws, 'Account ID is required');
          return;
        }

        this.wsServer.sendLog('info', `Starting login process for account ${accountId}`);
        const success = await this.loginAutomation.loginAccount(accountId);

        if (success) {
          this.wsServer.sendSuccess(ws, 'Login completed successfully');
        } else {
          this.wsServer.sendError(ws, 'Login failed');
        }
      } catch (error) {
        this.wsServer.sendError(ws, `Login error: ${error.message}`);
      }
    });

    // Command: Login multiple accounts
    this.wsServer.onCommand('login_accounts_batch', async (ws, data) => {
      try {
        const { accountIds } = data;
        if (!accountIds || !Array.isArray(accountIds)) {
          this.wsServer.sendError(ws, 'Account IDs array is required');
          return;
        }

        this.wsServer.sendLog('info', `Starting batch login for ${accountIds.length} accounts`);

        // Login accounts sequentially to avoid overwhelming the system
        for (const accountId of accountIds) {
          try {
            await this.loginAutomation.loginAccount(accountId);
            // Small delay between logins
            await new Promise(resolve => setTimeout(resolve, 2000));
          } catch (error) {
            this.wsServer.sendLog('error', `Failed to login account ${accountId}: ${error.message}`);
          }
        }

        this.wsServer.sendSuccess(ws, 'Batch login completed');
      } catch (error) {
        this.wsServer.sendError(ws, `Batch login error: ${error.message}`);
      }
    });

    // Command: Get automation status
    this.wsServer.onCommand('get_automation_status', async (ws, data) => {
      try {
        const status = this.followInteractAutomation.getStatus();
        this.wsServer.sendSuccess(ws, 'Automation status retrieved', { status });
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to get automation status: ${error.message}`);
      }
    });

    // Command: Pause automation
    this.wsServer.onCommand('pause_automation', async (ws, data) => {
      try {
        // TODO: Implement pause functionality
        this.wsServer.sendSuccess(ws, 'Automation paused (placeholder)');
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to pause automation: ${error.message}`);
      }
    });

    // Command: Create new account
    this.wsServer.onCommand('create_account', async (ws, data) => {
      try {
        const { username, password, useGoogleLogin, proxy } = data;

        // Validation
        if (!useGoogleLogin && (!username || !password)) {
          this.wsServer.sendError(ws, 'Username and password are required for non-Google accounts');
          return;
        }

        if (!proxy || !proxy.host || !proxy.port) {
          this.wsServer.sendError(ws, 'Proxy information is required');
          return;
        }

        // Create account object
        const { v4: uuidv4 } = require('uuid');
        const newAccount = {
          id: uuidv4(),
          username: username || null,
          password: password || null,
          useGoogleLogin: useGoogleLogin || false,
          status: 'not_logged_in',
          proxy: {
            host: proxy.host,
            port: parseInt(proxy.port),
            username: proxy.username || null,
            password: proxy.password || null,
            country: proxy.country || null,
            city: proxy.city || null,
            isActive: true
          },
          persona: null, // Will be assigned when creating browser context
          stats: {
            followsToday: 0,
            followsThisSession: 0,
            lastActivity: null
          }
        };

        // Add to database
        await this.db.addAccount(newAccount);

        this.wsServer.sendSuccess(ws, `Account created successfully`);
        this.wsServer.sendLog('info', `New account created: ${username || 'Google account'}`);

        // Send updated accounts list to all clients
        const accounts = await this.db.getAccounts();
        this.wsServer.broadcast({
          type: 'success',
          message: 'Accounts updated',
          data: { accounts }
        });

      } catch (error) {
        this.wsServer.sendError(ws, `Failed to create account: ${error.message}`);
      }
    });

    // Command: Test proxy
    this.wsServer.onCommand('test_proxy', async (ws, data) => {
      try {
        const { proxy } = data;

        if (!proxy || !proxy.host || !proxy.port) {
          this.wsServer.sendError(ws, 'Proxy host and port are required');
          return;
        }

        this.wsServer.sendLog('info', `Testing proxy ${proxy.host}:${proxy.port}`);

        // Test proxy connection
        const isActive = await this.testProxyConnection(proxy);

        // Send result back to client
        this.wsServer.sendToClient(ws, {
          type: 'proxy_test_result',
          isActive,
          proxy
        });

        this.wsServer.sendLog(
          isActive ? 'success' : 'error',
          `Proxy ${proxy.host}:${proxy.port} is ${isActive ? 'active' : 'inactive'}`
        );

      } catch (error) {
        this.wsServer.sendError(ws, `Failed to test proxy: ${error.message}`);
        this.wsServer.sendToClient(ws, {
          type: 'proxy_test_result',
          isActive: false,
          proxy: data.proxy
        });
      }
    });

    console.log(`[${formatTime()}] Command handlers registered`);
  }

  /**
   * Test proxy connection
   */
  async testProxyConnection(proxy) {
    try {
      const { chromium } = require('playwright');

      // Create proxy configuration
      const proxyConfig = {
        server: `http://${proxy.host}:${proxy.port}`
      };

      if (proxy.username && proxy.password) {
        proxyConfig.username = proxy.username;
        proxyConfig.password = proxy.password;
      }

      // Launch browser with proxy
      const browser = await chromium.launch({
        headless: true,
        proxy: proxyConfig
      });

      const context = await browser.newContext();
      const page = await context.newPage();

      // Test connection by visiting a simple endpoint
      const response = await page.goto('https://httpbin.org/ip', {
        waitUntil: 'networkidle',
        timeout: 10000
      });

      await browser.close();

      // Check if request was successful
      return response && response.status() === 200;

    } catch (error) {
      console.error('Proxy test error:', error);
      return false;
    }
  }

  /**
   * Dừng backend
   */
  async shutdown() {
    console.log(`[${formatTime()}] Shutting down backend...`);

    // Đóng tất cả browsers và stop automation
    if (this.followInteractAutomation) {
      await this.followInteractAutomation.stopAutomation();
    }

    if (this.loginAutomation) {
      await this.loginAutomation.closeAllBrowsers();
    }

    this.wsServer.stop();
    this.isRunning = false;
  }
}

// Khởi tạo và chạy backend
const backend = new TikTokAutomationBackend();

// Xử lý tín hiệu tắt ứng dụng
process.on('SIGINT', async () => {
  console.log('\nReceived SIGINT, shutting down gracefully...');
  await backend.shutdown();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM, shutting down gracefully...');
  await backend.shutdown();
  process.exit(0);
});

// Bắt đầu backend
backend.initialize().catch(error => {
  console.error('Failed to initialize backend:', error);
  process.exit(1);
});
