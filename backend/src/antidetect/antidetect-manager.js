/**
 * Antidetect Manager
 * <PERSON>u<PERSON>n lý việc áp dụng personas vào browser contexts
 */

const path = require('path');
const fs = require('fs').promises;
const PersonaGenerator = require('./persona-generator');
const { TIMEZONE_BY_REGION, LANGUAGE_BY_REGION } = require('./fingerprint-data');

class AntidetectManager {
  constructor() {
    this.personas = [];
    this.personasLoaded = false;
    this.personaGenerator = new PersonaGenerator();
  }

  /**
   * Load personas từ file
   * @returns {Promise<void>}
   */
  async loadPersonas() {
    if (this.personasLoaded) return;

    try {
      const personasPath = path.join(__dirname, '../../data/personas.json');
      const content = await fs.readFile(personasPath, 'utf-8');
      const data = JSON.parse(content);
      this.personas = data.personas || [];
      this.personasLoaded = true;
      console.log(`Loaded ${this.personas.length} personas`);
    } catch (error) {
      console.error('Error loading personas:', error);
      // Fallback: tạo một số personas mặc định
      this.personas = [
        this.personaGenerator.generateWindowsPersona('US'),
        this.personaGenerator.generateMacPersona('US'),
        this.personaGenerator.generateWindowsPersona('GB'),
        this.personaGenerator.generateMacPersona('CA')
      ];
      this.personasLoaded = true;
      console.log('Using fallback personas');
    }
  }

  /**
   * Lựa chọn persona ngẫu nhiên
   * @param {string} preferredRegion - Region ưu tiên (optional)
   * @returns {Object} - Persona object
   */
  async selectRandomPersona(preferredRegion = null) {
    await this.loadPersonas();

    let availablePersonas = this.personas;

    // Nếu có region ưu tiên, ưu tiên chọn persona từ region đó
    if (preferredRegion) {
      const regionPersonas = this.personas.filter(p => p.region === preferredRegion);
      if (regionPersonas.length > 0) {
        availablePersonas = regionPersonas;
      }
    }

    if (availablePersonas.length === 0) {
      throw new Error('No personas available');
    }

    const randomIndex = Math.floor(Math.random() * availablePersonas.length);
    return availablePersonas[randomIndex];
  }

  /**
   * Ánh xạ từ proxy location đến region code
   * @param {string} country - Mã quốc gia từ proxy
   * @returns {string} - Region code
   */
  mapCountryToRegion(country) {
    const mapping = {
      'US': 'US',
      'CA': 'CA', 
      'GB': 'GB',
      'UK': 'GB',
      'DE': 'DE',
      'FR': 'FR',
      'IT': 'IT',
      'ES': 'ES',
      'AU': 'AU',
      'JP': 'JP',
      'KR': 'KR',
      'CN': 'CN',
      'IN': 'IN',
      'BR': 'BR',
      'MX': 'MX'
    };

    return mapping[country?.toUpperCase()] || 'US';
  }

  /**
   * Tạo timezone và locale từ proxy location
   * @param {Object} proxy - Proxy object với country và city
   * @returns {Object} - { timezone, locale, languages }
   */
  generateLocaleFromProxy(proxy) {
    if (!proxy || !proxy.country) {
      return {
        timezone: 'America/New_York',
        locale: 'en-US',
        languages: ['en-US', 'en']
      };
    }

    const region = this.mapCountryToRegion(proxy.country);
    const timezones = TIMEZONE_BY_REGION[region] || TIMEZONE_BY_REGION['US'];
    const language = LANGUAGE_BY_REGION[region] || LANGUAGE_BY_REGION['US'];

    // Chọn timezone ngẫu nhiên từ region
    const timezone = timezones[Math.floor(Math.random() * timezones.length)];

    return {
      timezone,
      locale: language.primary,
      languages: [language.primary, ...language.secondary]
    };
  }

  /**
   * Tạo geolocation từ proxy
   * @param {Object} proxy - Proxy object
   * @returns {Object} - Geolocation object
   */
  generateGeolocationFromProxy(proxy) {
    if (!proxy || !proxy.country) {
      return {
        latitude: 40.7128,
        longitude: -74.0060,
        accuracy: 100
      };
    }

    // Sử dụng persona generator để tạo geolocation
    const region = this.mapCountryToRegion(proxy.country);
    return this.personaGenerator.generateGeolocation(region);
  }

  /**
   * Tạo browser context options từ persona và proxy
   * @param {Object} persona - Persona object
   * @param {Object} proxy - Proxy object (optional)
   * @returns {Object} - Playwright context options
   */
  createContextOptions(persona, proxy = null) {
    const localeInfo = proxy ? this.generateLocaleFromProxy(proxy) : {
      timezone: persona.timezone,
      locale: persona.language,
      languages: persona.languages
    };

    const geolocation = proxy ? this.generateGeolocationFromProxy(proxy) : persona.geolocation;

    const options = {
      userAgent: persona.userAgent,
      viewport: {
        width: persona.screen.width,
        height: persona.screen.height
      },
      screen: {
        width: persona.screen.width,
        height: persona.screen.height
      },
      locale: localeInfo.locale,
      timezoneId: localeInfo.timezone,
      geolocation: {
        latitude: geolocation.latitude,
        longitude: geolocation.longitude,
        accuracy: geolocation.accuracy
      },
      permissions: ['geolocation'],
      deviceScaleFactor: 1,
      isMobile: false,
      hasTouch: false,
      colorScheme: 'light',
      reducedMotion: 'no-preference',
      forcedColors: 'none',
      extraHTTPHeaders: {
        'Accept-Language': localeInfo.languages.join(',')
      }
    };

    // Thêm proxy nếu có
    if (proxy) {
      options.proxy = {
        server: `http://${proxy.host}:${proxy.port}`
      };

      if (proxy.username && proxy.password) {
        options.proxy.username = proxy.username;
        options.proxy.password = proxy.password;
      }
    }

    return options;
  }

  /**
   * Tạo browser launch options với antidetect
   * @returns {Object} - Browser launch options
   */
  createBrowserLaunchOptions() {
    return {
      args: [
        '--disable-features=WebRtcHideLocalIpsWithMdns',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-networking',
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-client-side-phishing-detection',
        '--disable-component-extensions-with-background-pages',
        '--disable-default-apps',
        '--disable-dev-shm-usage',
        '--disable-extensions',
        '--disable-features=TranslateUI',
        '--disable-hang-monitor',
        '--disable-ipc-flooding-protection',
        '--disable-popup-blocking',
        '--disable-prompt-on-repost',
        '--disable-sync',
        '--metrics-recording-only',
        '--no-first-run',
        '--no-default-browser-check',
        '--password-store=basic',
        '--use-mock-keychain'
      ]
    };
  }

  /**
   * Lấy persona theo ID
   * @param {string} personaId - ID của persona
   * @returns {Object|null} - Persona object hoặc null
   */
  async getPersonaById(personaId) {
    await this.loadPersonas();
    return this.personas.find(p => p.id === personaId) || null;
  }

  /**
   * Tạo script spoofing cho persona
   * @param {Object} persona - Persona object
   * @returns {string} - JavaScript code để inject
   */
  createSpoofingScript(persona) {
    return `
      // WebGL Spoofing
      (function() {
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
          if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
            return '${persona.webgl.vendor}';
          }
          if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL
            return '${persona.webgl.renderer}';
          }
          return getParameter.call(this, parameter);
        };

        const getParameter2 = WebGL2RenderingContext.prototype.getParameter;
        WebGL2RenderingContext.prototype.getParameter = function(parameter) {
          if (parameter === 37445) {
            return '${persona.webgl.vendor}';
          }
          if (parameter === 37446) {
            return '${persona.webgl.renderer}';
          }
          return getParameter2.call(this, parameter);
        };
      })();

      // Canvas Fingerprinting Protection
      (function() {
        const toDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function() {
          const result = toDataURL.apply(this, arguments);
          // Thêm noise nhẹ vào canvas
          const noise = '${persona.canvas.toDataURLNoise}';
          return result + noise.slice(0, 10);
        };
      })();

      // Screen properties
      Object.defineProperty(screen, 'width', { value: ${persona.screen.width} });
      Object.defineProperty(screen, 'height', { value: ${persona.screen.height} });
      Object.defineProperty(screen, 'availWidth', { value: ${persona.screen.availWidth} });
      Object.defineProperty(screen, 'availHeight', { value: ${persona.screen.availHeight} });
      Object.defineProperty(screen, 'colorDepth', { value: ${persona.screen.colorDepth} });
      Object.defineProperty(screen, 'pixelDepth', { value: ${persona.screen.pixelDepth} });

      // Hardware properties
      Object.defineProperty(navigator, 'deviceMemory', { value: ${persona.hardware.deviceMemory} });
      Object.defineProperty(navigator, 'hardwareConcurrency', { value: ${persona.hardware.hardwareConcurrency} });

      // Language properties
      Object.defineProperty(navigator, 'language', { value: '${persona.language}' });
      Object.defineProperty(navigator, 'languages', { value: ${JSON.stringify(persona.languages)} });

      console.log('🎭 Antidetect script loaded for persona: ${persona.id}');
    `;
  }
}

module.exports = AntidetectManager;
